<Application x:Class="ClaudeAuthSwitcher.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- 全局应用程序资源 -->
        
        <!-- 全局字体设置 -->
        <Style TargetType="{x:Type Window}">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <!-- 全局TextBlock样式 -->
        <Style TargetType="{x:Type TextBlock}">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        </Style>
        
        <!-- 全局Button样式 -->
        <Style TargetType="{x:Type Button}">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        </Style>
        
        <!-- 全局RadioButton样式 -->
        <Style TargetType="{x:Type RadioButton}">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        </Style>
        
    </Application.Resources>
</Application>
