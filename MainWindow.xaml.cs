using ClaudeAuthSwitcher.Models;
using ClaudeAuthSwitcher.Services;
using System;
using System.Linq;
using System.Text;
using System.Windows;

namespace ClaudeAuthSwitcher
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        /// <summary>
        /// 初始化窗口
        /// </summary>
        private void InitializeWindow()
        {
            // 更新权限状态显示
            UpdatePermissionStatus();
            
            // 刷新环境变量状态
            RefreshEnvironmentStatus();
            
            // 添加欢迎信息
            AppendResult("Claude Auth Switcher 已启动");
            AppendResult("请选择需要的认证方式进行切换");
        }

        /// <summary>
        /// 更新权限状态显示
        /// </summary>
        private void UpdatePermissionStatus()
        {
            PermissionStatusText.Text = $"当前权限状态：{PermissionHelper.GetCurrentPrivilegeStatus()}";
        }

        /// <summary>
        /// 刷新环境变量状态显示
        /// </summary>
        private void RefreshEnvironmentStatus()
        {
            var target = GetSelectedTarget();
            var status = EnvironmentManager.GetClaudeEnvironmentStatus(target);
            
            var statusBuilder = new StringBuilder();
            statusBuilder.AppendLine($"环境变量级别：{(target == EnvironmentManager.VariableTarget.User ? "用户级别" : "系统级别")}");
            statusBuilder.AppendLine();
            
            foreach (var kvp in status)
            {
                var value = kvp.Value;
                if (string.IsNullOrEmpty(value))
                {
                    statusBuilder.AppendLine($"{kvp.Key}: (未设置)");
                }
                else
                {
                    // 敏感信息脱敏显示
                    var maskedValue = MaskSensitiveValue(value);
                    statusBuilder.AppendLine($"{kvp.Key}: {maskedValue}");
                }
            }
            
            StatusText.Text = statusBuilder.ToString();
        }

        /// <summary>
        /// 脱敏显示敏感信息
        /// </summary>
        /// <param name="value">原始值</param>
        /// <returns>脱敏后的值</returns>
        private string MaskSensitiveValue(string value)
        {
            if (string.IsNullOrEmpty(value) || value.Length <= 10)
            {
                return value;
            }
            
            // 显示前6位和后4位，中间用*代替
            return $"{value.Substring(0, 6)}***{value.Substring(value.Length - 4)}";
        }

        /// <summary>
        /// 获取当前选择的环境变量目标级别
        /// </summary>
        /// <returns>环境变量目标级别</returns>
        private EnvironmentManager.VariableTarget GetSelectedTarget()
        {
            return UserLevelRadio.IsChecked == true 
                ? EnvironmentManager.VariableTarget.User 
                : EnvironmentManager.VariableTarget.Machine;
        }

        /// <summary>
        /// 添加操作结果信息
        /// </summary>
        /// <param name="message">消息内容</param>
        private void AppendResult(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            ResultText.Text += $"[{timestamp}] {message}\n";
            
            // 自动滚动到底部
            if (ResultText.Parent is ScrollViewer scrollViewer)
            {
                scrollViewer.ScrollToEnd();
            }
        }

        /// <summary>
        /// 应用认证配置
        /// </summary>
        /// <param name="config">认证配置</param>
        private void ApplyAuthConfig(AuthConfig config)
        {
            var target = GetSelectedTarget();
            var isSystemLevel = target == EnvironmentManager.VariableTarget.Machine;
            
            // 检查权限
            if (!PermissionHelper.RequestAdministratorPrivilegesIfNeeded(isSystemLevel, $"应用{config.Name}配置"))
            {
                AppendResult($"权限不足，无法应用{config.Name}配置");
                return;
            }
            
            AppendResult($"开始应用{config.Name}配置...");
            
            // 应用环境变量配置
            var results = EnvironmentManager.SetEnvironmentVariables(config.EnvironmentVariables, target);
            
            // 显示设置结果
            foreach (var kvp in results)
            {
                var status = kvp.Value ? "成功" : "失败";
                var action = config.EnvironmentVariables[kvp.Key] == null ? "清除" : "设置";
                AppendResult($"  {action} {kvp.Key}: {status}");
            }
            
            // 验证配置
            var validation = EnvironmentManager.ValidateConfiguration(config.EnvironmentVariables, target);
            var allValid = validation.All(v => v.Value);
            
            if (allValid)
            {
                AppendResult($"✓ {config.Name}配置应用成功！");
            }
            else
            {
                AppendResult($"⚠ {config.Name}配置应用完成，但部分验证失败");
            }
            
            // 刷新状态显示
            RefreshEnvironmentStatus();
        }

        #region 事件处理

        /// <summary>
        /// OAuth按钮点击事件
        /// </summary>
        private void OAuthButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyAuthConfig(AuthConfig.OAuthConfig);
        }

        /// <summary>
        /// AnyRouter按钮点击事件
        /// </summary>
        private void AnyRouterButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyAuthConfig(AuthConfig.AnyRouterConfig);
        }

        /// <summary>
        /// Moonshot按钮点击事件
        /// </summary>
        private void MoonshotButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyAuthConfig(AuthConfig.MoonshotConfig);
        }

        /// <summary>
        /// 刷新状态按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            AppendResult("刷新环境变量状态...");
            RefreshEnvironmentStatus();
            AppendResult("状态刷新完成");
        }

        /// <summary>
        /// 清除所有按钮点击事件
        /// </summary>
        private void ClearAllButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "确定要清除所有Claude相关环境变量吗？\n\n此操作将删除所有ANTHROPIC_*环境变量。",
                "确认清除",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question,
                MessageBoxResult.No);
                
            if (result == MessageBoxResult.Yes)
            {
                var target = GetSelectedTarget();
                var isSystemLevel = target == EnvironmentManager.VariableTarget.Machine;
                
                if (!PermissionHelper.RequestAdministratorPrivilegesIfNeeded(isSystemLevel, "清除所有环境变量"))
                {
                    AppendResult("权限不足，无法清除环境变量");
                    return;
                }
                
                AppendResult("开始清除所有Claude相关环境变量...");
                var clearResults = EnvironmentManager.ClearClaudeEnvironmentVariables(target);
                
                foreach (var kvp in clearResults)
                {
                    var status = kvp.Value ? "成功" : "失败";
                    AppendResult($"  清除 {kvp.Key}: {status}");
                }
                
                AppendResult("✓ 清除操作完成");
                RefreshEnvironmentStatus();
            }
        }

        #endregion
    }
}
