using ClaudeAuthSwitcher.Models;
using ClaudeAuthSwitcher.Services;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace ClaudeAuthSwitcher
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            InitializeWindow();

            // 注册窗口关闭事件
            Closing += MainWindow_Closing;
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void MainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // 保存用户设置
                SaveUserSettings();
                AppendResult("用户设置已保存");
            }
            catch (Exception ex)
            {
                // 不阻止窗口关闭，只记录错误
                System.Diagnostics.Debug.WriteLine($"保存设置时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 键盘快捷键处理
        /// </summary>
        private void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // Ctrl + 数字键快捷方式
                if (e.KeyboardDevice.Modifiers == ModifierKeys.Control)
                {
                    switch (e.Key)
                    {
                        case Key.D1:
                            OAuthButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            break;
                        case Key.D2:
                            AnyRouterButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            break;
                        case Key.D3:
                            MoonshotButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            break;
                        case Key.R:
                            RefreshButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            break;
                        case Key.Delete:
                            ClearAllButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            break;
                    }
                }

                // F5 刷新
                if (e.Key == Key.F5)
                {
                    RefreshButton_Click(this, new RoutedEventArgs());
                    e.Handled = true;
                }

                // Escape 键关闭应用
                if (e.Key == Key.Escape)
                {
                    Close();
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                AppendResult($"处理快捷键时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化窗口
        /// </summary>
        private void InitializeWindow()
        {
            // 加载用户设置
            LoadUserSettings();

            // 更新权限状态显示
            UpdatePermissionStatus();

            // 刷新环境变量状态
            RefreshEnvironmentStatus();

            // 添加欢迎信息
            AppendResult("🎉 Claude Auth Switcher v1.0.0 已启动");
            AppendResult("📋 支持三种认证方式：OAuth标准登录、AnyRouter代理、Moonshot代理");
            AppendResult("⌨️ 快捷键：Ctrl+1/2/3 切换认证，F5 刷新，Ctrl+Del 清除，Esc 退出");
            AppendResult("🚀 请选择需要的认证方式进行切换");
        }

        /// <summary>
        /// 加载用户设置
        /// </summary>
        private void LoadUserSettings()
        {
            try
            {
                // 加载上次选择的目标级别
                var lastTarget = SettingsManager.GetLastSelectedTarget();
                if (lastTarget == EnvironmentManager.VariableTarget.User)
                {
                    UserLevelRadio.IsChecked = true;
                }
                else
                {
                    SystemLevelRadio.IsChecked = true;
                }

                // 加载窗口大小（如果需要的话，当前是固定大小）
                // var (width, height) = SettingsManager.GetWindowSize();
                // Width = width;
                // Height = height;
            }
            catch (Exception ex)
            {
                AppendResult($"加载用户设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存用户设置
        /// </summary>
        private void SaveUserSettings()
        {
            try
            {
                // 保存目标级别选择
                var target = GetSelectedTarget();
                SettingsManager.SaveSelectedTarget(target);

                // 保存窗口大小
                SettingsManager.SaveWindowSize(Width, Height);
            }
            catch (Exception ex)
            {
                AppendResult($"保存用户设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新权限状态显示
        /// </summary>
        private void UpdatePermissionStatus()
        {
            PermissionStatusText.Text = $"当前权限状态：{PermissionHelper.GetCurrentPrivilegeStatus()}";
        }

        /// <summary>
        /// 刷新环境变量状态显示
        /// </summary>
        private void RefreshEnvironmentStatus()
        {
            var target = GetSelectedTarget();
            var status = EnvironmentManager.GetClaudeEnvironmentStatus(target);
            
            var statusBuilder = new StringBuilder();
            statusBuilder.AppendLine($"环境变量级别：{(target == EnvironmentManager.VariableTarget.User ? "用户级别" : "系统级别")}");
            statusBuilder.AppendLine();
            
            foreach (var kvp in status)
            {
                var value = kvp.Value;
                if (string.IsNullOrEmpty(value))
                {
                    statusBuilder.AppendLine($"{kvp.Key}: (未设置)");
                }
                else
                {
                    // 敏感信息脱敏显示
                    var maskedValue = MaskSensitiveValue(value);
                    statusBuilder.AppendLine($"{kvp.Key}: {maskedValue}");
                }
            }
            
            StatusText.Text = statusBuilder.ToString();
        }

        /// <summary>
        /// 脱敏显示敏感信息
        /// </summary>
        /// <param name="value">原始值</param>
        /// <returns>脱敏后的值</returns>
        private string MaskSensitiveValue(string value)
        {
            if (string.IsNullOrEmpty(value) || value.Length <= 10)
            {
                return value;
            }
            
            // 显示前6位和后4位，中间用*代替
            return $"{value.Substring(0, 6)}***{value.Substring(value.Length - 4)}";
        }

        /// <summary>
        /// 获取当前选择的环境变量目标级别
        /// </summary>
        /// <returns>环境变量目标级别</returns>
        private EnvironmentManager.VariableTarget GetSelectedTarget()
        {
            return UserLevelRadio.IsChecked == true 
                ? EnvironmentManager.VariableTarget.User 
                : EnvironmentManager.VariableTarget.Machine;
        }

        /// <summary>
        /// 添加操作结果信息
        /// </summary>
        /// <param name="message">消息内容</param>
        private void AppendResult(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            ResultText.Text += $"[{timestamp}] {message}\n";
            
            // 自动滚动到底部
            if (ResultText.Parent is ScrollViewer scrollViewer)
            {
                scrollViewer.ScrollToEnd();
            }
        }

        /// <summary>
        /// 应用认证配置
        /// </summary>
        /// <param name="config">认证配置</param>
        private async void ApplyAuthConfig(AuthConfig config)
        {
            var target = GetSelectedTarget();
            var isSystemLevel = target == EnvironmentManager.VariableTarget.Machine;

            // 检查权限
            if (!PermissionHelper.RequestAdministratorPrivilegesIfNeeded(isSystemLevel, $"应用{config.Name}配置"))
            {
                AppendResult($"权限不足，无法应用{config.Name}配置");
                return;
            }

            AppendResult($"开始应用{config.Name}配置...");

            try
            {
                // 应用环境变量配置（使用增强版方法）
                var batchResult = EnvironmentManager.SetEnvironmentVariablesEnhanced(config.EnvironmentVariables, target);

                // 显示详细结果
                foreach (var result in batchResult.Results)
                {
                    AppendResult(result.GetFormattedText(false));
                }

                // 显示汇总
                AppendResult($"配置应用汇总: {batchResult.GetSummary()}");

                // 执行完整验证
                AppendResult("正在验证配置...");
                var validationResult = await ConfigurationValidator.PerformFullValidationAsync(config, target, true);

                // 显示验证结果
                foreach (var result in validationResult.Results)
                {
                    AppendResult(result.GetFormattedText(false));
                }

                // 保存使用的配置
                SettingsManager.SaveUsedAuthConfig(config.Name);

                // 最终状态
                if (batchResult.IsSuccess && validationResult.IsSuccess)
                {
                    AppendResult($"✓ {config.Name} 配置应用并验证成功！");
                }
                else if (batchResult.IsSuccess)
                {
                    AppendResult($"⚠ {config.Name} 配置应用成功，但验证有警告");
                }
                else
                {
                    AppendResult($"✗ {config.Name} 配置应用失败");
                }
            }
            catch (Exception ex)
            {
                AppendResult($"✗ 应用{config.Name}配置时发生异常: {ex.Message}");
            }
            finally
            {
                // 刷新状态显示
                RefreshEnvironmentStatus();

                // 保存用户设置
                SaveUserSettings();
            }
        }

        #region 事件处理

        /// <summary>
        /// 目标级别变更事件
        /// </summary>
        private void TargetLevel_Changed(object sender, RoutedEventArgs e)
        {
            try
            {
                // 更新权限状态显示
                UpdatePermissionStatus();

                // 刷新环境变量状态
                RefreshEnvironmentStatus();

                // 保存用户设置
                SaveUserSettings();

                var target = GetSelectedTarget();
                var levelName = target == EnvironmentManager.VariableTarget.User ? "用户级别" : "系统级别";
                AppendResult($"已切换到{levelName}环境变量设置");
            }
            catch (Exception ex)
            {
                AppendResult($"切换目标级别时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// OAuth按钮点击事件
        /// </summary>
        private void OAuthButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyAuthConfig(AuthConfig.OAuthConfig);
        }

        /// <summary>
        /// AnyRouter按钮点击事件
        /// </summary>
        private void AnyRouterButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyAuthConfig(AuthConfig.AnyRouterConfig);
        }

        /// <summary>
        /// Moonshot按钮点击事件
        /// </summary>
        private void MoonshotButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyAuthConfig(AuthConfig.MoonshotConfig);
        }

        /// <summary>
        /// 刷新状态按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            AppendResult("刷新环境变量状态...");
            RefreshEnvironmentStatus();
            AppendResult("状态刷新完成");
        }

        /// <summary>
        /// 清除所有按钮点击事件
        /// </summary>
        private async void ClearAllButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "确定要清除所有Claude相关环境变量吗？\n\n此操作将删除所有ANTHROPIC_*环境变量。",
                "确认清除",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question,
                MessageBoxResult.No);

            if (result == MessageBoxResult.Yes)
            {
                var target = GetSelectedTarget();
                var isSystemLevel = target == EnvironmentManager.VariableTarget.Machine;

                if (!PermissionHelper.RequestAdministratorPrivilegesIfNeeded(isSystemLevel, "清除所有环境变量"))
                {
                    AppendResult("权限不足，无法清除环境变量");
                    return;
                }

                try
                {
                    AppendResult("开始清除所有Claude相关环境变量...");

                    // 使用OAuth配置来清除所有变量（OAuth配置将所有变量设为null）
                    var clearConfig = AuthConfig.OAuthConfig;
                    var batchResult = EnvironmentManager.SetEnvironmentVariablesEnhanced(clearConfig.EnvironmentVariables, target);

                    // 显示详细结果
                    foreach (var operationResult in batchResult.Results)
                    {
                        AppendResult(operationResult.GetFormattedText(false));
                    }

                    // 显示汇总
                    AppendResult($"清除操作汇总: {batchResult.GetSummary()}");

                    // 验证清除结果
                    AppendResult("正在验证清除结果...");
                    var validationResult = await ConfigurationValidator.PerformFullValidationAsync(clearConfig, target, false);

                    foreach (var validationRes in validationResult.Results)
                    {
                        AppendResult(validationRes.GetFormattedText(false));
                    }

                    if (batchResult.IsSuccess && validationResult.IsSuccess)
                    {
                        AppendResult("✓ 所有Claude相关环境变量已成功清除");
                    }
                    else
                    {
                        AppendResult("⚠ 清除操作完成，但部分操作可能未成功");
                    }
                }
                catch (Exception ex)
                {
                    AppendResult($"✗ 清除环境变量时发生异常: {ex.Message}");
                }
                finally
                {
                    RefreshEnvironmentStatus();
                    SaveUserSettings();
                }
            }
        }

        #endregion
    }
}
