# <PERSON> Auth Switcher v1.0.0

## 🚀 快速启动

1. 双击 ClaudeAuthSwitcher.exe 启动程序
2. 选择环境变量级别（推荐用户级别）
3. 点击需要的认证方式按钮
4. 查看操作结果确认配置成功

## ⌨️ 快捷键

- Ctrl+1: OAuth标准登录
- Ctrl+2: AnyRouter代理
- Ctrl+3: Moonshot代理
- F5: 刷新状态
- Ctrl+Del: 清除所有
- Esc: 退出程序

## 📋 认证方式说明

### OAuth 标准登录
- 使用Claude官方认证流程
- 清除所有自定义环境变量
- 适用于网络环境良好的情况

### AnyRouter 代理
- 通过AnyRouter代理服务访问
- 适用于网络受限环境
- 使用预配置的认证令牌

### Moonshot 代理
- 通过Moonshot代理服务访问
- 适用于特定网络环境
- 使用预配置的API密钥

## 🔧 系统要求

- Windows 10/11 x64
- 无需安装.NET运行时
- 建议内存：2GB以上

## 📖 更多信息

- 详细使用说明：docs\USER_MANUAL.md
- 发布说明：docs\RELEASE_NOTES.md
- 部署检查清单：docs\DEPLOYMENT_CHECKLIST.md

## 🆘 常见问题

Q: 为什么需要管理员权限？
A: 只有选择"系统级别"时才需要，推荐使用"用户级别"。

Q: 如何知道配置是否生效？
A: 查看"当前环境变量状态"和"操作结果"区域。

Q: 程序关闭后设置会丢失吗？
A: 不会，环境变量设置是永久的。

## 📞 技术支持

如有问题请查看docs目录下的详细文档。

---
Claude Auth Switcher v1.0.0
Copyright © 2024
