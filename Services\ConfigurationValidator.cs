using ClaudeAuthSwitcher.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ClaudeAuthSwitcher.Services
{
    /// <summary>
    /// 配置验证服务
    /// 负责验证认证配置的有效性和连通性
    /// </summary>
    public static class ConfigurationValidator
    {
        /// <summary>
        /// 验证认证配置是否正确应用
        /// </summary>
        /// <param name="config">认证配置</param>
        /// <param name="target">目标级别</param>
        /// <returns>验证结果</returns>
        public static OperationResult ValidateAuthConfiguration(AuthConfig config, EnvironmentManager.VariableTarget target)
        {
            try
            {
                var validationResults = new List<string>();
                var hasErrors = false;

                // 验证每个环境变量
                foreach (var kvp in config.EnvironmentVariables)
                {
                    var actualValue = EnvironmentManager.GetEnvironmentVariable(kvp.Key, target);
                    var expectedValue = kvp.Value;

                    if (string.Equals(actualValue, expectedValue, StringComparison.Ordinal))
                    {
                        var status = expectedValue == null ? "已清除" : "设置正确";
                        validationResults.Add($"✓ {kvp.Key}: {status}");
                    }
                    else
                    {
                        hasErrors = true;
                        var expected = expectedValue ?? "(清除)";
                        var actual = actualValue ?? "(未设置)";
                        validationResults.Add($"✗ {kvp.Key}: 期望 '{expected}', 实际 '{actual}'");
                    }
                }

                if (hasErrors)
                {
                    return OperationResult.Error(
                        $"{config.Name} 配置验证失败",
                        null,
                        validationResults.ToArray()
                    );
                }
                else
                {
                    return OperationResult.Success(
                        $"{config.Name} 配置验证成功",
                        validationResults.ToArray()
                    );
                }
            }
            catch (Exception ex)
            {
                return OperationResult.Error(
                    $"验证 {config.Name} 配置时发生异常",
                    ex
                );
            }
        }

        /// <summary>
        /// 检查API端点连通性（异步）
        /// </summary>
        /// <param name="config">认证配置</param>
        /// <returns>连通性检查结果</returns>
        public static async Task<OperationResult> CheckApiConnectivityAsync(AuthConfig config)
        {
            try
            {
                // 获取基础URL
                var baseUrl = config.EnvironmentVariables.GetValueOrDefault("ANTHROPIC_BASE_URL");
                
                if (string.IsNullOrEmpty(baseUrl))
                {
                    return OperationResult.Info(
                        $"{config.Name} 使用默认API端点",
                        "未设置自定义ANTHROPIC_BASE_URL，将使用Claude官方API"
                    );
                }

                // 创建HTTP客户端进行连通性测试
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(10);

                // 尝试连接到API端点
                var response = await httpClient.GetAsync(baseUrl);
                
                if (response.IsSuccessStatusCode)
                {
                    return OperationResult.Success(
                        $"{config.Name} API端点连通性正常",
                        $"URL: {baseUrl}",
                        $"状态码: {response.StatusCode}",
                        $"响应时间: < 10秒"
                    );
                }
                else
                {
                    return OperationResult.Warning(
                        $"{config.Name} API端点响应异常",
                        $"URL: {baseUrl}",
                        $"状态码: {response.StatusCode}",
                        "这可能是正常的，某些API端点不支持GET请求"
                    );
                }
            }
            catch (HttpRequestException ex)
            {
                return OperationResult.Warning(
                    $"{config.Name} API端点连接失败: {ex.Message}",
                    "这可能是网络问题或API端点配置问题",
                    "请检查网络连接和代理设置"
                );
            }
            catch (TaskCanceledException)
            {
                return OperationResult.Warning(
                    $"{config.Name} API端点连接超时",
                    "连接超时（>10秒）",
                    "这可能是网络延迟或服务器响应慢"
                );
            }
            catch (Exception ex)
            {
                return OperationResult.Error(
                    $"检查 {config.Name} API连通性时发生异常",
                    ex
                );
            }
        }

        /// <summary>
        /// 验证API密钥格式
        /// </summary>
        /// <param name="config">认证配置</param>
        /// <returns>密钥格式验证结果</returns>
        public static OperationResult ValidateApiKeyFormat(AuthConfig config)
        {
            try
            {
                var validationResults = new List<string>();
                var hasWarnings = false;

                // 检查ANTHROPIC_API_KEY
                var apiKey = config.EnvironmentVariables.GetValueOrDefault("ANTHROPIC_API_KEY");
                if (!string.IsNullOrEmpty(apiKey))
                {
                    if (apiKey.StartsWith("sk-") && apiKey.Length > 20)
                    {
                        validationResults.Add("✓ ANTHROPIC_API_KEY 格式正确");
                    }
                    else
                    {
                        hasWarnings = true;
                        validationResults.Add("⚠ ANTHROPIC_API_KEY 格式可能不正确");
                    }
                }

                // 检查ANTHROPIC_AUTH_TOKEN
                var authToken = config.EnvironmentVariables.GetValueOrDefault("ANTHROPIC_AUTH_TOKEN");
                if (!string.IsNullOrEmpty(authToken))
                {
                    if (authToken.StartsWith("sk-") && authToken.Length > 20)
                    {
                        validationResults.Add("✓ ANTHROPIC_AUTH_TOKEN 格式正确");
                    }
                    else
                    {
                        hasWarnings = true;
                        validationResults.Add("⚠ ANTHROPIC_AUTH_TOKEN 格式可能不正确");
                    }
                }

                // 检查ANTHROPIC_BASE_URL
                var baseUrl = config.EnvironmentVariables.GetValueOrDefault("ANTHROPIC_BASE_URL");
                if (!string.IsNullOrEmpty(baseUrl))
                {
                    if (Uri.TryCreate(baseUrl, UriKind.Absolute, out var uri) && 
                        (uri.Scheme == "http" || uri.Scheme == "https"))
                    {
                        validationResults.Add("✓ ANTHROPIC_BASE_URL 格式正确");
                    }
                    else
                    {
                        hasWarnings = true;
                        validationResults.Add("⚠ ANTHROPIC_BASE_URL 格式可能不正确");
                    }
                }

                if (validationResults.Count == 0)
                {
                    return OperationResult.Info(
                        $"{config.Name} 无需验证API密钥格式",
                        "此配置清除了所有API密钥"
                    );
                }

                if (hasWarnings)
                {
                    return OperationResult.Warning(
                        $"{config.Name} API密钥格式验证完成（有警告）",
                        validationResults.ToArray()
                    );
                }
                else
                {
                    return OperationResult.Success(
                        $"{config.Name} API密钥格式验证通过",
                        validationResults.ToArray()
                    );
                }
            }
            catch (Exception ex)
            {
                return OperationResult.Error(
                    $"验证 {config.Name} API密钥格式时发生异常",
                    ex
                );
            }
        }

        /// <summary>
        /// 执行完整的配置验证
        /// </summary>
        /// <param name="config">认证配置</param>
        /// <param name="target">目标级别</param>
        /// <param name="checkConnectivity">是否检查连通性</param>
        /// <returns>完整验证结果</returns>
        public static async Task<BatchOperationResult> PerformFullValidationAsync(
            AuthConfig config, 
            EnvironmentManager.VariableTarget target, 
            bool checkConnectivity = false)
        {
            var batchResult = new BatchOperationResult();

            try
            {
                // 1. 验证配置是否正确应用
                var configValidation = ValidateAuthConfiguration(config, target);
                batchResult.AddResult(configValidation);

                // 2. 验证API密钥格式
                var keyFormatValidation = ValidateApiKeyFormat(config);
                batchResult.AddResult(keyFormatValidation);

                // 3. 检查API连通性（如果需要）
                if (checkConnectivity)
                {
                    var connectivityResult = await CheckApiConnectivityAsync(config);
                    batchResult.AddResult(connectivityResult);
                }

                return batchResult;
            }
            catch (Exception ex)
            {
                batchResult.AddResult(OperationResult.Error(
                    $"执行 {config.Name} 完整验证时发生异常",
                    ex
                ));
                return batchResult;
            }
        }
    }
}
