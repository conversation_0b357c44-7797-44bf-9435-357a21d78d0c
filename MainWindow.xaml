<Window x:Class="ClaudeAuthSwitcher.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Claude Auth Switcher - Claude Code认证切换器"
        Height="600" Width="800"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen"
        KeyDown="MainWindow_KeyDown">
        <!-- Icon="icon.ico" -->
    
    <Window.Resources>
        <!-- 颜色资源 -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#0078D4"/>
        <SolidColorBrush x:Key="PrimaryHoverBrush" Color="#106EBE"/>
        <SolidColorBrush x:Key="PrimaryPressedBrush" Color="#005A9E"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#6C757D"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#28A745"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FFC107"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#DC3545"/>
        <SolidColorBrush x:Key="LightBrush" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="DarkBrush" Color="#343A40"/>

        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="4"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryHoverBrush}"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="6"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryPressedBrush}"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.4" BlurRadius="2"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                                <Setter Property="Effect" Value="{x:Null}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 小按钮样式 -->
        <Style x:Key="SmallButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
        </Style>

        <!-- 成功按钮样式 -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        </Style>

        <!-- 警告按钮样式 -->
        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
            <Setter Property="Foreground" Value="Black"/>
        </Style>

        <!-- 危险按钮样式 -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
        </Style>

        <!-- 状态区域样式 -->
        <Style x:Key="StatusPanelStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource LightBrush}"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.1" BlurRadius="3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 标题样式 -->
        <Style x:Key="SectionTitleStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="{StaticResource DarkBrush}"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>

        <!-- 代码文本样式 -->
        <Style x:Key="CodeTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas, Courier New, monospace"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <!-- RadioButton样式 -->
        <Style TargetType="RadioButton">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,20,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="{StaticResource DarkBrush}"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>      <!-- 标题区域 -->
            <RowDefinition Height="Auto"/>      <!-- 设置区域 -->
            <RowDefinition Height="Auto"/>      <!-- 操作区域 -->
            <RowDefinition Height="*"/>         <!-- 状态区域 -->
            <RowDefinition Height="*"/>         <!-- 结果区域 -->
            <RowDefinition Height="Auto"/>      <!-- 控制区域 -->
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="24">
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
            </Border.Effect>
            <StackPanel>
                <TextBlock Text="🔐 Claude Auth Switcher"
                          FontSize="28"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"/>
                <TextBlock Text="Claude Code认证切换器 v1.0.0 - 快速切换认证方式"
                          FontSize="13"
                          Foreground="#E3F2FD"
                          HorizontalAlignment="Center"
                          Margin="0,8,0,0"/>
            </StackPanel>
        </Border>

        <!-- 设置区域 -->
        <Border Grid.Row="1" Style="{StaticResource StatusPanelStyle}">
            <StackPanel>
                <TextBlock Text="⚙️ 环境变量级别设置" Style="{StaticResource SectionTitleStyle}"/>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                    <RadioButton x:Name="UserLevelRadio"
                                Content="👤 用户级别（推荐）"
                                IsChecked="True"
                                Margin="0,0,40,0"
                                Checked="TargetLevel_Changed"/>
                    <RadioButton x:Name="SystemLevelRadio"
                                Content="🔧 系统级别（需要管理员权限）"
                                Checked="TargetLevel_Changed"/>
                </StackPanel>
                <Border Background="#E3F2FD" CornerRadius="4" Padding="12" BorderBrush="{StaticResource PrimaryBrush}" BorderThickness="1">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🛡️" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock x:Name="PermissionStatusText"
                                  Text="当前权限状态：标准用户权限"
                                  FontSize="12"
                                  Foreground="{StaticResource DarkBrush}"
                                  VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- 操作区域 -->
        <Border Grid.Row="2" Style="{StaticResource StatusPanelStyle}">
            <StackPanel>
                <TextBlock Text="🚀 选择认证方式" Style="{StaticResource SectionTitleStyle}"/>
                <UniformGrid Rows="1" Columns="3">
                    <Button x:Name="OAuthButton"
                           Style="{StaticResource SuccessButtonStyle}"
                           Click="OAuthButton_Click"
                           ToolTip="OAuth 标准登录 (Ctrl+1)">
                        <StackPanel>
                            <TextBlock Text="🔐 OAuth 标准登录" FontWeight="Bold" FontSize="15"/>
                            <TextBlock Text="使用Claude官方认证" FontSize="11" Margin="0,4,0,0" Opacity="0.9"/>
                            <TextBlock Text="Ctrl+1" FontSize="9" Margin="0,2,0,0" Opacity="0.7" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="AnyRouterButton"
                           Style="{StaticResource ModernButtonStyle}"
                           Click="AnyRouterButton_Click"
                           ToolTip="AnyRouter 代理 (Ctrl+2)">
                        <StackPanel>
                            <TextBlock Text="🌐 AnyRouter 代理" FontWeight="Bold" FontSize="15"/>
                            <TextBlock Text="通过AnyRouter访问" FontSize="11" Margin="0,4,0,0" Opacity="0.9"/>
                            <TextBlock Text="Ctrl+2" FontSize="9" Margin="0,2,0,0" Opacity="0.7" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="MoonshotButton"
                           Style="{StaticResource ModernButtonStyle}"
                           Click="MoonshotButton_Click"
                           ToolTip="Moonshot 代理 (Ctrl+3)">
                        <StackPanel>
                            <TextBlock Text="🌙 Moonshot 代理" FontWeight="Bold" FontSize="15"/>
                            <TextBlock Text="通过Moonshot访问" FontSize="11" Margin="0,4,0,0" Opacity="0.9"/>
                            <TextBlock Text="Ctrl+3" FontSize="9" Margin="0,2,0,0" Opacity="0.7" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </UniformGrid>
            </StackPanel>
        </Border>

        <!-- 状态区域 -->
        <Border Grid.Row="3" Style="{StaticResource StatusPanelStyle}">
            <StackPanel>
                <TextBlock Text="📊 当前环境变量状态" Style="{StaticResource SectionTitleStyle}"/>
                <Border BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="4" Background="White" Padding="12">
                    <TextBlock x:Name="StatusText"
                              Style="{StaticResource CodeTextStyle}"
                              Background="Transparent"
                              Padding="0"/>
                </Border>
            </StackPanel>
        </Border>

        <!-- 结果区域 -->
        <Border Grid.Row="4" Style="{StaticResource StatusPanelStyle}">
            <StackPanel>
                <TextBlock Text="📝 操作结果" Style="{StaticResource SectionTitleStyle}"/>
                <Border BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="4" Background="White">
                    <ScrollViewer Height="120" VerticalScrollBarVisibility="Auto" Padding="4">
                        <TextBlock x:Name="ResultText"
                                  Style="{StaticResource CodeTextStyle}"
                                  Background="Transparent"
                                  Padding="8"/>
                    </ScrollViewer>
                </Border>
            </StackPanel>
        </Border>

        <!-- 控制区域 -->
        <Border Grid.Row="5" Padding="16">
            <Grid>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="RefreshButton"
                           Content="🔄 刷新状态"
                           Style="{StaticResource SmallButtonStyle}"
                           Click="RefreshButton_Click"
                           ToolTip="刷新当前环境变量状态显示 (F5 或 Ctrl+R)"/>
                    <Button x:Name="ClearAllButton"
                           Content="🗑️ 清除所有"
                           Style="{StaticResource DangerButtonStyle}"
                           Click="ClearAllButton_Click"
                           ToolTip="清除所有Claude相关环境变量 (Ctrl+Del)"
                           Margin="8,5,5,5"
                           Padding="12,6"/>
                </StackPanel>

                <!-- 快捷键提示 -->
                <TextBlock Text="💡 快捷键: Ctrl+1/2/3 切换认证 | F5 刷新 | Ctrl+Del 清除 | Esc 退出"
                          FontSize="10"
                          Foreground="#6C757D"
                          HorizontalAlignment="Right"
                          VerticalAlignment="Bottom"
                          Margin="0,0,0,2"/>
            </Grid>
        </Border>
    </Grid>
</Window>
