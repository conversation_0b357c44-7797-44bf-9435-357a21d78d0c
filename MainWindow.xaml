<Window x:Class="ClaudeAuthSwitcher.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Claude Auth Switcher - Claude Code认证切换器"
        Height="600" Width="800"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen"
        Icon="icon.ico">
    
    <Window.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#0078D4"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#106EBE"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 小按钮样式 -->
        <Style x:Key="SmallButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="5"/>
        </Style>

        <!-- 状态区域样式 -->
        <Style x:Key="StatusPanelStyle" TargetType="Border">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="5"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="10"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>      <!-- 标题区域 -->
            <RowDefinition Height="Auto"/>      <!-- 设置区域 -->
            <RowDefinition Height="Auto"/>      <!-- 操作区域 -->
            <RowDefinition Height="*"/>         <!-- 状态区域 -->
            <RowDefinition Height="*"/>         <!-- 结果区域 -->
            <RowDefinition Height="Auto"/>      <!-- 控制区域 -->
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <Border Grid.Row="0" Background="#0078D4" Padding="20">
            <StackPanel>
                <TextBlock Text="Claude Auth Switcher" 
                          FontSize="24" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          HorizontalAlignment="Center"/>
                <TextBlock Text="Claude Code认证切换器 v1.0.0" 
                          FontSize="12" 
                          Foreground="#E0E0E0" 
                          HorizontalAlignment="Center" 
                          Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- 设置区域 -->
        <Border Grid.Row="1" Style="{StaticResource StatusPanelStyle}">
            <StackPanel>
                <TextBlock Text="环境变量级别设置" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                <StackPanel Orientation="Horizontal">
                    <RadioButton x:Name="UserLevelRadio" 
                                Content="用户级别（推荐）" 
                                IsChecked="True" 
                                Margin="0,0,30,0"
                                VerticalAlignment="Center"/>
                    <RadioButton x:Name="SystemLevelRadio" 
                                Content="系统级别（需要管理员权限）" 
                                VerticalAlignment="Center"/>
                </StackPanel>
                <TextBlock x:Name="PermissionStatusText" 
                          Text="当前权限状态：标准用户权限" 
                          FontSize="12" 
                          Foreground="#666666" 
                          Margin="0,10,0,0"/>
            </StackPanel>
        </Border>

        <!-- 操作区域 -->
        <Border Grid.Row="2" Style="{StaticResource StatusPanelStyle}">
            <StackPanel>
                <TextBlock Text="选择认证方式" FontWeight="Bold" FontSize="14" Margin="0,0,0,15"/>
                <UniformGrid Rows="1" Columns="3">
                    <Button x:Name="OAuthButton" 
                           Style="{StaticResource ModernButtonStyle}"
                           Click="OAuthButton_Click">
                        <StackPanel>
                            <TextBlock Text="OAuth 标准登录" FontWeight="Bold" FontSize="16"/>
                            <TextBlock Text="使用Claude官方认证" FontSize="12" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="AnyRouterButton" 
                           Style="{StaticResource ModernButtonStyle}"
                           Click="AnyRouterButton_Click">
                        <StackPanel>
                            <TextBlock Text="AnyRouter 代理" FontWeight="Bold" FontSize="16"/>
                            <TextBlock Text="通过AnyRouter访问" FontSize="12" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="MoonshotButton" 
                           Style="{StaticResource ModernButtonStyle}"
                           Click="MoonshotButton_Click">
                        <StackPanel>
                            <TextBlock Text="Moonshot 代理" FontWeight="Bold" FontSize="16"/>
                            <TextBlock Text="通过Moonshot访问" FontSize="12" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Button>
                </UniformGrid>
            </StackPanel>
        </Border>

        <!-- 状态区域 -->
        <Border Grid.Row="3" Style="{StaticResource StatusPanelStyle}">
            <StackPanel>
                <TextBlock Text="当前环境变量状态" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                <TextBlock x:Name="StatusText" 
                          FontFamily="Consolas" 
                          FontSize="12" 
                          Background="White" 
                          Padding="10" 
                          TextWrapping="Wrap"/>
            </StackPanel>
        </Border>

        <!-- 结果区域 -->
        <Border Grid.Row="4" Style="{StaticResource StatusPanelStyle}">
            <StackPanel>
                <TextBlock Text="操作结果" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                <ScrollViewer Height="120" VerticalScrollBarVisibility="Auto">
                    <TextBlock x:Name="ResultText" 
                              FontFamily="Consolas" 
                              FontSize="12" 
                              Background="White" 
                              Padding="10" 
                              TextWrapping="Wrap"/>
                </ScrollViewer>
            </StackPanel>
        </Border>

        <!-- 控制区域 -->
        <Border Grid.Row="5" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="RefreshButton" 
                       Content="刷新状态" 
                       Style="{StaticResource SmallButtonStyle}"
                       Click="RefreshButton_Click"/>
                <Button x:Name="ClearAllButton" 
                       Content="清除所有" 
                       Style="{StaticResource SmallButtonStyle}"
                       Click="ClearAllButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
