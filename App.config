<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <appSettings>
        <!-- 用户设置 -->
        <add key="LastSelectedTarget" value="User" />
        <add key="LastUsedAuthConfig" value="OAuth" />
        <add key="WindowWidth" value="800" />
        <add key="WindowHeight" value="600" />
        
        <!-- 应用程序设置 -->
        <add key="ApplicationName" value="Claude Auth Switcher" />
        <add key="ApplicationVersion" value="1.0.0" />
        <add key="EnableConnectivityCheck" value="true" />
        <add key="ConnectivityCheckTimeout" value="10" />
        
        <!-- 调试设置 -->
        <add key="EnableDebugLogging" value="false" />
        <add key="LogLevel" value="Info" />
    </appSettings>
    
    <system.diagnostics>
        <trace autoflush="true">
            <listeners>
                <add name="textWriterTraceListener" 
                     type="System.Diagnostics.TextWriterTraceListener" 
                     initializeData="debug.log" />
            </listeners>
        </trace>
    </system.diagnostics>
    
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <!-- 程序集绑定重定向（如果需要） -->
        </assemblyBinding>
    </runtime>
</configuration>
