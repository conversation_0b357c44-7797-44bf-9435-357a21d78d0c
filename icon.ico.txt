# 应用程序图标说明

由于无法直接创建二进制的.ico文件，这里提供图标的说明和获取方式：

## 图标设计要求
- 尺寸：16x16, 32x32, 48x48, 256x256 像素
- 格式：ICO格式
- 主题：Claude/AI认证相关
- 颜色：蓝色主题 (#0078D4)

## 建议的图标元素
- 🔐 锁形图标（代表认证）
- 🔄 切换箭头（代表切换功能）
- 💻 电脑图标（代表桌面应用）
- <PERSON>的"C"字母

## 图标获取方式

### 方式1：在线图标生成器
1. 访问 https://www.favicon-generator.org/
2. 上传一个PNG图片（建议256x256）
3. 生成ICO文件并下载
4. 重命名为 icon.ico

### 方式2：使用现有图标
可以从以下网站下载合适的图标：
- https://icons8.com/
- https://www.flaticon.com/
- https://feathericons.com/

### 方式3：自己设计
使用图像编辑软件（如GIMP、Photoshop）创建图标：
1. 创建256x256像素的画布
2. 设计蓝色主题的认证/切换图标
3. 导出为ICO格式

## 临时解决方案
如果暂时没有图标文件，可以：
1. 删除MainWindow.xaml中的 Icon="icon.ico" 属性
2. 或者使用Windows默认图标

## 使用说明
将生成的icon.ico文件放在项目根目录下，构建时会自动包含到输出中。
