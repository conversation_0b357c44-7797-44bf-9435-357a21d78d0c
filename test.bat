@echo off
echo ========================================
echo Claude Auth Switcher 测试脚本
echo ========================================

:: 检查.NET 6.0是否安装
echo 正在检查.NET 6.0运行时...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到.NET运行时，请先安装.NET 6.0
    echo 下载地址：https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo .NET运行时版本：
dotnet --version

:: 构建项目
echo.
echo 正在构建项目...
dotnet build -c Debug
if errorlevel 1 (
    echo 错误：项目构建失败
    pause
    exit /b 1
)

:: 运行项目
echo.
echo 正在启动应用程序...
echo ========================================
echo 测试指南：
echo 1. 检查界面是否正常显示
echo 2. 测试用户级别和系统级别切换
echo 3. 测试三种认证方式切换
echo 4. 测试刷新状态功能
echo 5. 测试清除所有功能
echo 6. 测试快捷键功能
echo 7. 检查权限提升是否正常工作
echo ========================================
echo.

dotnet run

echo.
echo 应用程序已退出。
pause
