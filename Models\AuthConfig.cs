using System.Collections.Generic;

namespace ClaudeAuthSwitcher.Models
{
    /// <summary>
    /// 认证配置模型类
    /// 定义Claude Code的三种认证方式配置
    /// </summary>
    public class AuthConfig
    {
        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 环境变量配置字典
        /// Key: 环境变量名称, Value: 环境变量值（null表示清除该变量）
        /// </summary>
        public Dictionary<string, string?> EnvironmentVariables { get; set; } = new();

        /// <summary>
        /// OAuth 标准登录配置
        /// 清除所有自定义环境变量，恢复Claude官方默认认证流程
        /// </summary>
        public static AuthConfig OAuthConfig => new()
        {
            Name = "OAuth 标准登录",
            Description = "使用Claude官方OAuth认证，清除所有自定义配置",
            EnvironmentVariables = new Dictionary<string, string?>
            {
                { "ANTHROPIC_AUTH_TOKEN", null },    // 清除自定义token
                { "ANTHROPIC_BASE_URL", null },      // 清除自定义URL
                { "ANTHROPIC_API_KEY", null }        // 清除自定义API Key
            }
        };

        /// <summary>
        /// AnyRouter 代理认证配置
        /// 通过AnyRouter代理服务访问Claude API
        /// </summary>
        public static AuthConfig AnyRouterConfig => new()
        {
            Name = "AnyRouter 代理",
            Description = "通过AnyRouter代理服务访问Claude API",
            EnvironmentVariables = new Dictionary<string, string?>
            {
                { "ANTHROPIC_AUTH_TOKEN", "sk-evhOEnqDX4YB68E1EAvAfrNVUYXXWvJwzPf3mm63gfSOxHHl" },
                { "ANTHROPIC_BASE_URL", "https://anyrouter.top" },
                { "ANTHROPIC_API_KEY", null }        // 清除API Key
            }
        };

        /// <summary>
        /// Moonshot 代理认证配置
        /// 通过Moonshot代理服务访问Claude API
        /// </summary>
        public static AuthConfig MoonshotConfig => new()
        {
            Name = "Moonshot 代理",
            Description = "通过Moonshot代理服务访问Claude API",
            EnvironmentVariables = new Dictionary<string, string?>
            {
                { "ANTHROPIC_BASE_URL", "https://api.moonshot.cn/anthropic/" },
                { "ANTHROPIC_API_KEY", "sk-JcWzWW8FEnbrEvmvIY6N4vbcFzPxqXJBiFSbqej3Ff2uJBsY" },
                { "ANTHROPIC_AUTH_TOKEN", null }     // 清除Auth Token
            }
        };

        /// <summary>
        /// 获取所有预设配置
        /// </summary>
        public static List<AuthConfig> GetAllConfigs() => new()
        {
            OAuthConfig,
            AnyRouterConfig,
            MoonshotConfig
        };
    }
}
