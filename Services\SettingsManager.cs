using System;
using System.Configuration;
using System.Diagnostics;

namespace ClaudeAuthSwitcher.Services
{
    /// <summary>
    /// 用户设置管理服务
    /// 负责保存和加载用户偏好设置
    /// </summary>
    public static class SettingsManager
    {
        /// <summary>
        /// 获取上次选择的环境变量目标级别
        /// </summary>
        /// <returns>环境变量目标级别</returns>
        public static EnvironmentManager.VariableTarget GetLastSelectedTarget()
        {
            try
            {
                var setting = ConfigurationManager.AppSettings["LastSelectedTarget"] ?? "User";
                return Enum.TryParse<EnvironmentManager.VariableTarget>(setting, out var target) 
                    ? target 
                    : EnvironmentManager.VariableTarget.User;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取上次选择的目标级别失败: {ex.Message}");
                return EnvironmentManager.VariableTarget.User;
            }
        }

        /// <summary>
        /// 保存选择的环境变量目标级别
        /// </summary>
        /// <param name="target">目标级别</param>
        public static void SaveSelectedTarget(EnvironmentManager.VariableTarget target)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                
                if (config.AppSettings.Settings["LastSelectedTarget"] != null)
                {
                    config.AppSettings.Settings["LastSelectedTarget"].Value = target.ToString();
                }
                else
                {
                    config.AppSettings.Settings.Add("LastSelectedTarget", target.ToString());
                }
                
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
                
                Debug.WriteLine($"已保存目标级别设置: {target}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存目标级别设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取上次使用的认证配置
        /// </summary>
        /// <returns>认证配置名称</returns>
        public static string GetLastUsedAuthConfig()
        {
            try
            {
                return ConfigurationManager.AppSettings["LastUsedAuthConfig"] ?? "OAuth";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取上次使用的认证配置失败: {ex.Message}");
                return "OAuth";
            }
        }

        /// <summary>
        /// 保存使用的认证配置
        /// </summary>
        /// <param name="configName">配置名称</param>
        public static void SaveUsedAuthConfig(string configName)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                
                if (config.AppSettings.Settings["LastUsedAuthConfig"] != null)
                {
                    config.AppSettings.Settings["LastUsedAuthConfig"].Value = configName;
                }
                else
                {
                    config.AppSettings.Settings.Add("LastUsedAuthConfig", configName);
                }
                
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
                
                Debug.WriteLine($"已保存认证配置设置: {configName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存认证配置设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取窗口大小设置
        /// </summary>
        /// <returns>窗口宽度和高度</returns>
        public static (double Width, double Height) GetWindowSize()
        {
            try
            {
                var widthStr = ConfigurationManager.AppSettings["WindowWidth"] ?? "800";
                var heightStr = ConfigurationManager.AppSettings["WindowHeight"] ?? "600";
                
                var width = double.TryParse(widthStr, out var w) ? w : 800;
                var height = double.TryParse(heightStr, out var h) ? h : 600;
                
                return (width, height);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取窗口大小设置失败: {ex.Message}");
                return (800, 600);
            }
        }

        /// <summary>
        /// 保存窗口大小设置
        /// </summary>
        /// <param name="width">窗口宽度</param>
        /// <param name="height">窗口高度</param>
        public static void SaveWindowSize(double width, double height)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                
                // 保存宽度
                if (config.AppSettings.Settings["WindowWidth"] != null)
                {
                    config.AppSettings.Settings["WindowWidth"].Value = width.ToString();
                }
                else
                {
                    config.AppSettings.Settings.Add("WindowWidth", width.ToString());
                }
                
                // 保存高度
                if (config.AppSettings.Settings["WindowHeight"] != null)
                {
                    config.AppSettings.Settings["WindowHeight"].Value = height.ToString();
                }
                else
                {
                    config.AppSettings.Settings.Add("WindowHeight", height.ToString());
                }
                
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
                
                Debug.WriteLine($"已保存窗口大小设置: {width}x{height}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存窗口大小设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置所有设置为默认值
        /// </summary>
        public static void ResetToDefaults()
        {
            try
            {
                SaveSelectedTarget(EnvironmentManager.VariableTarget.User);
                SaveUsedAuthConfig("OAuth");
                SaveWindowSize(800, 600);
                
                Debug.WriteLine("所有设置已重置为默认值");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"重置设置失败: {ex.Message}");
            }
        }
    }
}
