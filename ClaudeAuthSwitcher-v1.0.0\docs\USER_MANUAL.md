# Claude Auth Switcher 用户手册

## 📖 目录
1. [快速开始](#快速开始)
2. [界面介绍](#界面介绍)
3. [功能详解](#功能详解)
4. [快捷键参考](#快捷键参考)
5. [常见问题](#常见问题)
6. [故障排除](#故障排除)

## 🚀 快速开始

### 第一步：启动应用程序
1. 双击 `ClaudeAuthSwitcher.exe` 启动应用程序
2. 应用程序将显示欢迎信息和当前状态

### 第二步：选择环境变量级别
- **用户级别（推荐）**: 仅对当前用户生效，无需管理员权限
- **系统级别**: 对所有用户生效，需要管理员权限

### 第三步：选择认证方式
点击以下任一按钮应用相应的认证配置：
- **🔐 OAuth 标准登录**: 使用Claude官方认证
- **🌐 AnyRouter 代理**: 通过AnyRouter访问
- **🌙 Moonshot 代理**: 通过Moonshot访问

### 第四步：验证配置
查看"操作结果"区域确认配置是否成功应用。

## 🖥️ 界面介绍

### 主界面布局
```
┌─────────────────────────────────────────┐
│           🔐 Claude Auth Switcher        │  ← 标题栏
├─────────────────────────────────────────┤
│  ⚙️ 环境变量级别设置                      │  ← 设置区域
│  ○ 用户级别  ○ 系统级别                  │
├─────────────────────────────────────────┤
│  🚀 选择认证方式                          │  ← 操作区域
│  [OAuth] [AnyRouter] [Moonshot]         │
├─────────────────────────────────────────┤
│  📊 当前环境变量状态                      │  ← 状态区域
│  ANTHROPIC_API_KEY: sk-xxx...xxx        │
├─────────────────────────────────────────┤
│  📝 操作结果                              │  ← 结果区域
│  [时间] ✓ 操作成功                       │
├─────────────────────────────────────────┤
│  [🔄 刷新状态] [🗑️ 清除所有]              │  ← 控制区域
└─────────────────────────────────────────┘
```

### 界面元素说明

#### 1. 标题栏
- 显示应用程序名称和版本信息
- 蓝色背景，现代化设计

#### 2. 设置区域
- **环境变量级别选择**: 单选按钮组，选择用户级别或系统级别
- **权限状态显示**: 显示当前运行权限状态

#### 3. 操作区域
- **三个认证方式按钮**: 点击应用相应的认证配置
- **快捷键提示**: 每个按钮显示对应的快捷键

#### 4. 状态区域
- **实时状态显示**: 显示当前环境变量的值
- **敏感信息脱敏**: API密钥只显示前6位和后4位

#### 5. 结果区域
- **操作日志**: 显示所有操作的详细结果
- **时间戳**: 每条记录都有时间标记
- **状态图标**: ✓ 成功, ⚠ 警告, ✗ 错误, ℹ 信息

#### 6. 控制区域
- **刷新状态按钮**: 更新当前状态显示
- **清除所有按钮**: 删除所有Claude相关环境变量

## 🔧 功能详解

### 环境变量级别

#### 用户级别（推荐）
- **作用范围**: 仅对当前登录用户生效
- **权限要求**: 无需管理员权限
- **存储位置**: 用户注册表 `HKEY_CURRENT_USER\Environment`
- **适用场景**: 个人开发环境，多用户共享计算机

#### 系统级别
- **作用范围**: 对所有用户生效
- **权限要求**: 需要管理员权限
- **存储位置**: 系统注册表 `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment`
- **适用场景**: 企业环境，单用户计算机

### 认证方式详解

#### OAuth 标准登录
```
环境变量配置：
- ANTHROPIC_AUTH_TOKEN: (清除)
- ANTHROPIC_BASE_URL: (清除)
- ANTHROPIC_API_KEY: (清除)
```
- **用途**: 恢复Claude官方默认认证流程
- **特点**: 清除所有自定义配置，使用OAuth登录
- **适用**: 网络环境良好，可直接访问Claude官方API

#### AnyRouter 代理
```
环境变量配置：
- ANTHROPIC_AUTH_TOKEN: sk-evhOEnqDX4YB68E1EAvAfrNVUYXXWvJwzPf3mm63gfSOxHHl
- ANTHROPIC_BASE_URL: https://anyrouter.top
- ANTHROPIC_API_KEY: (清除)
```
- **用途**: 通过AnyRouter代理服务访问Claude API
- **特点**: 使用预配置的认证令牌和代理URL
- **适用**: 网络受限环境，需要代理访问

#### Moonshot 代理
```
环境变量配置：
- ANTHROPIC_BASE_URL: https://api.moonshot.cn/anthropic/
- ANTHROPIC_API_KEY: sk-JcWzWW8FEnbrEvmvIY6N4vbcFzPxqXJBiFSbqej3Ff2uJBsY
- ANTHROPIC_AUTH_TOKEN: (清除)
```
- **用途**: 通过Moonshot代理服务访问Claude API
- **特点**: 使用预配置的API密钥和代理URL
- **适用**: 特定网络环境，Moonshot服务用户

### 配置验证功能

#### 自动验证
每次应用配置后，系统会自动执行以下验证：

1. **环境变量验证**: 检查环境变量是否正确设置
2. **格式验证**: 验证API密钥和URL格式是否正确
3. **连通性测试**: 可选的网络连通性检查

#### 验证结果解读
- **✓ 成功**: 配置正确，验证通过
- **⚠ 警告**: 配置可能有问题，但不影响基本功能
- **✗ 错误**: 配置失败，需要检查和修复

## ⌨️ 快捷键参考

### 认证切换快捷键
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+1` | OAuth 标准登录 | 切换到Claude官方认证 |
| `Ctrl+2` | AnyRouter 代理 | 切换到AnyRouter代理 |
| `Ctrl+3` | Moonshot 代理 | 切换到Moonshot代理 |

### 操作快捷键
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `F5` | 刷新状态 | 更新环境变量状态显示 |
| `Ctrl+R` | 刷新状态 | 同F5功能 |
| `Ctrl+Delete` | 清除所有 | 删除所有Claude相关环境变量 |
| `Esc` | 退出程序 | 关闭应用程序 |

### 快捷键使用技巧
- 快捷键在应用程序获得焦点时有效
- 可以通过工具提示查看每个按钮的快捷键
- 快捷键操作会显示在操作结果区域

## ❓ 常见问题

### Q1: 为什么需要管理员权限？
**A**: 只有在选择"系统级别"环境变量设置时才需要管理员权限。推荐使用"用户级别"设置，无需管理员权限。

### Q2: 如何知道配置是否生效？
**A**: 查看以下几个地方：
- "当前环境变量状态"区域显示最新配置
- "操作结果"区域显示详细的验证信息
- 可以使用"刷新状态"按钮更新显示

### Q3: 可以自定义API密钥吗？
**A**: 当前版本不支持自定义API密钥，使用的是预配置的密钥。未来版本会考虑添加此功能。

### Q4: 如何恢复到默认设置？
**A**: 点击"OAuth 标准登录"按钮，这会清除所有自定义环境变量，恢复到Claude官方默认设置。

### Q5: 程序关闭后设置会丢失吗？
**A**: 不会。环境变量设置是永久的，程序关闭后仍然有效。用户偏好设置（如级别选择）也会自动保存。

### Q6: 可以同时使用多种认证方式吗？
**A**: 不可以。每次只能使用一种认证方式，新的设置会覆盖之前的配置。

## 🔧 故障排除

### 权限相关问题

#### 问题：UAC权限提升失败
**症状**: 点击系统级别操作时，UAC对话框出现但操作失败
**解决方案**:
1. 确保以管理员身份运行应用程序
2. 检查UAC设置是否过于严格
3. 尝试右键点击程序，选择"以管理员身份运行"

#### 问题：权限不足错误
**症状**: 显示"权限不足，无法应用配置"
**解决方案**:
1. 切换到"用户级别"设置
2. 或者以管理员身份重新启动程序
3. 检查当前用户是否有足够权限

### 环境变量问题

#### 问题：环境变量设置失败
**症状**: 操作结果显示"设置环境变量失败"
**解决方案**:
1. 检查系统权限设置
2. 确认环境变量名称和值的有效性
3. 重启应用程序重试
4. 检查系统注册表是否可写

#### 问题：配置验证失败
**症状**: 环境变量设置成功但验证失败
**解决方案**:
1. 点击"刷新状态"按钮更新显示
2. 重启应用程序
3. 手动检查系统环境变量设置

### 网络连接问题

#### 问题：连通性检查失败
**症状**: 显示"API端点连接失败"或"连接超时"
**解决方案**:
1. 检查网络连接是否正常
2. 确认防火墙设置不阻止连接
3. 检查代理设置是否正确
4. 连通性检查失败不影响基本功能使用

### 应用程序问题

#### 问题：程序启动失败
**症状**: 双击程序无响应或出现错误对话框
**解决方案**:
1. 确认系统满足最低要求（Windows 10 x64）
2. 检查是否有杀毒软件阻止运行
3. 尝试以管理员身份运行
4. 重新下载程序文件

#### 问题：界面显示异常
**症状**: 界面元素重叠或显示不完整
**解决方案**:
1. 检查系统DPI设置
2. 确认显示器分辨率设置
3. 重启应用程序
4. 检查系统字体设置

### 获取更多帮助

如果以上解决方案无法解决问题，请：

1. **记录详细信息**: 包括错误消息、操作步骤、系统环境
2. **查看操作结果**: 应用程序内的详细错误信息
3. **检查系统日志**: Windows事件查看器中的相关错误
4. **联系支持**: 通过GitHub Issues或其他渠道报告问题

---

**文档版本**: v1.0.0  
**最后更新**: 2024年1月  
**适用版本**: Claude Auth Switcher v1.0.0
