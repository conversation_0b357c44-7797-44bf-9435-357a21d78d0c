using System;
using System.Collections.Generic;
using System.Linq;

namespace ClaudeAuthSwitcher.Models
{
    /// <summary>
    /// 操作结果类型枚举
    /// </summary>
    public enum OperationResultType
    {
        Success,    // 成功
        Warning,    // 警告
        Error,      // 错误
        Info        // 信息
    }

    /// <summary>
    /// 操作结果模型
    /// 用于统一处理各种操作的结果反馈
    /// </summary>
    public class OperationResult
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 结果类型
        /// </summary>
        public OperationResultType Type { get; set; }

        /// <summary>
        /// 主要消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 详细信息列表
        /// </summary>
        public List<string> Details { get; set; } = new();

        /// <summary>
        /// 异常信息（如果有）
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 操作时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="message">成功消息</param>
        /// <param name="details">详细信息</param>
        /// <returns>操作结果</returns>
        public static OperationResult Success(string message, params string[] details)
        {
            return new OperationResult
            {
                IsSuccess = true,
                Type = OperationResultType.Success,
                Message = message,
                Details = details.ToList()
            };
        }

        /// <summary>
        /// 创建警告结果
        /// </summary>
        /// <param name="message">警告消息</param>
        /// <param name="details">详细信息</param>
        /// <returns>操作结果</returns>
        public static OperationResult Warning(string message, params string[] details)
        {
            return new OperationResult
            {
                IsSuccess = true,
                Type = OperationResultType.Warning,
                Message = message,
                Details = details.ToList()
            };
        }

        /// <summary>
        /// 创建错误结果
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常信息</param>
        /// <param name="details">详细信息</param>
        /// <returns>操作结果</returns>
        public static OperationResult Error(string message, Exception? exception = null, params string[] details)
        {
            return new OperationResult
            {
                IsSuccess = false,
                Type = OperationResultType.Error,
                Message = message,
                Exception = exception,
                Details = details.ToList()
            };
        }

        /// <summary>
        /// 创建信息结果
        /// </summary>
        /// <param name="message">信息消息</param>
        /// <param name="details">详细信息</param>
        /// <returns>操作结果</returns>
        public static OperationResult Info(string message, params string[] details)
        {
            return new OperationResult
            {
                IsSuccess = true,
                Type = OperationResultType.Info,
                Message = message,
                Details = details.ToList()
            };
        }

        /// <summary>
        /// 添加详细信息
        /// </summary>
        /// <param name="detail">详细信息</param>
        public void AddDetail(string detail)
        {
            Details.Add(detail);
        }

        /// <summary>
        /// 获取格式化的结果文本
        /// </summary>
        /// <param name="includeTimestamp">是否包含时间戳</param>
        /// <returns>格式化的结果文本</returns>
        public string GetFormattedText(bool includeTimestamp = true)
        {
            var prefix = Type switch
            {
                OperationResultType.Success => "✓",
                OperationResultType.Warning => "⚠",
                OperationResultType.Error => "✗",
                OperationResultType.Info => "ℹ",
                _ => ""
            };

            var timestamp = includeTimestamp ? $"[{Timestamp:HH:mm:ss}] " : "";
            var result = $"{timestamp}{prefix} {Message}";

            if (Details.Any())
            {
                result += "\n" + string.Join("\n", Details.Select(d => $"  • {d}"));
            }

            if (Exception != null)
            {
                result += $"\n  错误详情: {Exception.Message}";
            }

            return result;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>格式化的结果文本</returns>
        public override string ToString()
        {
            return GetFormattedText();
        }
    }

    /// <summary>
    /// 批量操作结果
    /// </summary>
    public class BatchOperationResult
    {
        /// <summary>
        /// 所有操作结果
        /// </summary>
        public List<OperationResult> Results { get; set; } = new();

        /// <summary>
        /// 整体操作是否成功
        /// </summary>
        public bool IsSuccess => Results.All(r => r.IsSuccess);

        /// <summary>
        /// 成功的操作数量
        /// </summary>
        public int SuccessCount => Results.Count(r => r.IsSuccess);

        /// <summary>
        /// 失败的操作数量
        /// </summary>
        public int FailureCount => Results.Count(r => !r.IsSuccess);

        /// <summary>
        /// 总操作数量
        /// </summary>
        public int TotalCount => Results.Count;

        /// <summary>
        /// 添加操作结果
        /// </summary>
        /// <param name="result">操作结果</param>
        public void AddResult(OperationResult result)
        {
            Results.Add(result);
        }

        /// <summary>
        /// 获取汇总信息
        /// </summary>
        /// <returns>汇总信息</returns>
        public string GetSummary()
        {
            if (TotalCount == 0)
            {
                return "没有执行任何操作";
            }

            if (IsSuccess)
            {
                return $"所有操作成功完成 ({SuccessCount}/{TotalCount})";
            }
            else
            {
                return $"部分操作失败 (成功: {SuccessCount}, 失败: {FailureCount}, 总计: {TotalCount})";
            }
        }

        /// <summary>
        /// 获取格式化的详细结果
        /// </summary>
        /// <returns>格式化的详细结果</returns>
        public string GetDetailedResults()
        {
            var summary = GetSummary();
            var details = string.Join("\n", Results.Select(r => r.GetFormattedText()));
            
            return $"{summary}\n\n{details}";
        }
    }
}
