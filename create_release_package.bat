@echo off
echo ========================================
echo 创建Claude Auth Switcher发布包
echo ========================================

set RELEASE_DIR=ClaudeAuthSwitcher-v1.0.0
set SOURCE_DIR=publishsingle-file

:: 清理之前的发布包
if exist %RELEASE_DIR% rmdir /s /q %RELEASE_DIR%

:: 创建发布包目录结构
echo 正在创建发布包目录结构...
mkdir %RELEASE_DIR%
mkdir %RELEASE_DIR%\docs

:: 复制主程序文件
echo 正在复制主程序文件...
copy %SOURCE_DIR%\ClaudeAuthSwitcher.exe %RELEASE_DIR%\
copy %SOURCE_DIR%\ClaudeAuthSwitcher.dll.config %RELEASE_DIR%\

:: 复制文档文件
echo 正在复制文档文件...
copy README.md %RELEASE_DIR%\
copy USER_MANUAL.md %RELEASE_DIR%\docs\
copy RELEASE_NOTES.md %RELEASE_DIR%\docs\
copy DEPLOYMENT_CHECKLIST.md %RELEASE_DIR%\docs\

:: 创建快速启动说明
echo 正在创建快速启动说明...
echo # Claude Auth Switcher v1.0.0 > %RELEASE_DIR%\快速开始.txt
echo. >> %RELEASE_DIR%\快速开始.txt
echo ## 快速启动 >> %RELEASE_DIR%\快速开始.txt
echo 1. 双击 ClaudeAuthSwitcher.exe 启动程序 >> %RELEASE_DIR%\快速开始.txt
echo 2. 选择环境变量级别（推荐用户级别） >> %RELEASE_DIR%\快速开始.txt
echo 3. 点击需要的认证方式按钮 >> %RELEASE_DIR%\快速开始.txt
echo 4. 查看操作结果确认配置成功 >> %RELEASE_DIR%\快速开始.txt
echo. >> %RELEASE_DIR%\快速开始.txt
echo ## 快捷键 >> %RELEASE_DIR%\快速开始.txt
echo - Ctrl+1: OAuth标准登录 >> %RELEASE_DIR%\快速开始.txt
echo - Ctrl+2: AnyRouter代理 >> %RELEASE_DIR%\快速开始.txt
echo - Ctrl+3: Moonshot代理 >> %RELEASE_DIR%\快速开始.txt
echo - F5: 刷新状态 >> %RELEASE_DIR%\快速开始.txt
echo - Ctrl+Del: 清除所有 >> %RELEASE_DIR%\快速开始.txt
echo - Esc: 退出程序 >> %RELEASE_DIR%\快速开始.txt
echo. >> %RELEASE_DIR%\快速开始.txt
echo ## 更多信息 >> %RELEASE_DIR%\快速开始.txt
echo 详细使用说明请查看 docs\USER_MANUAL.md >> %RELEASE_DIR%\快速开始.txt
echo 发布说明请查看 docs\RELEASE_NOTES.md >> %RELEASE_DIR%\快速开始.txt

:: 显示发布包信息
echo.
echo ========================================
echo 发布包创建完成！
echo ========================================
echo 发布包目录：%RELEASE_DIR%
echo 主程序：%RELEASE_DIR%\ClaudeAuthSwitcher.exe
echo 文档目录：%RELEASE_DIR%\docs\
echo.
echo 文件列表：
dir /b %RELEASE_DIR%
echo.
echo 文档列表：
dir /b %RELEASE_DIR%\docs
echo ========================================

pause
