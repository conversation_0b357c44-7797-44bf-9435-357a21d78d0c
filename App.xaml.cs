using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Threading;

namespace ClaudeAuthSwitcher
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        /// <summary>
        /// 应用程序启动事件
        /// </summary>
        /// <param name="e">启动事件参数</param>
        protected override void OnStartup(StartupEventArgs e)
        {
            // 设置全局异常处理
            SetupExceptionHandling();
            
            // 调用基类方法
            base.OnStartup(e);
            
            // 记录应用程序启动
            Debug.WriteLine("Claude Auth Switcher 应用程序已启动");
        }

        /// <summary>
        /// 应用程序退出事件
        /// </summary>
        /// <param name="e">退出事件参数</param>
        protected override void OnExit(ExitEventArgs e)
        {
            Debug.WriteLine("Claude Auth Switcher 应用程序正在退出");
            base.OnExit(e);
        }

        /// <summary>
        /// 设置全局异常处理
        /// </summary>
        private void SetupExceptionHandling()
        {
            // 处理UI线程未捕获的异常
            DispatcherUnhandledException += App_DispatcherUnhandledException;
            
            // 处理非UI线程未捕获的异常
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        }

        /// <summary>
        /// UI线程未捕获异常处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">异常事件参数</param>
        private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                var errorMessage = $"应用程序发生未处理的异常：\n\n{e.Exception.Message}\n\n详细信息：\n{e.Exception}";
                
                Debug.WriteLine($"UI线程异常: {e.Exception}");
                
                MessageBox.Show(
                    errorMessage,
                    "Claude Auth Switcher - 错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                
                // 标记异常已处理，防止应用程序崩溃
                e.Handled = true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"异常处理器本身发生异常: {ex}");
            }
        }

        /// <summary>
        /// 非UI线程未捕获异常处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">异常事件参数</param>
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                var errorMessage = exception?.ToString() ?? "未知异常";
                
                Debug.WriteLine($"非UI线程异常: {errorMessage}");
                
                // 由于这是非UI线程，需要使用Dispatcher调用UI操作
                Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        $"应用程序发生严重错误：\n\n{errorMessage}",
                        "Claude Auth Switcher - 严重错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"非UI线程异常处理器发生异常: {ex}");
            }
        }
    }
}
