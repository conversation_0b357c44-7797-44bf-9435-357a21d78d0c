@echo off
echo ========================================
echo Claude Auth Switcher 构建脚本
echo ========================================

:: 设置变量
set PROJECT_NAME=ClaudeAuthSwitcher
set OUTPUT_DIR=publish
set FRAMEWORK=net6.0-windows
set RUNTIME=win-x64

:: 清理之前的构建
echo 正在清理之前的构建...
if exist %OUTPUT_DIR% rmdir /s /q %OUTPUT_DIR%
if exist bin rmdir /s /q bin
if exist obj rmdir /s /q obj

:: 还原NuGet包
echo 正在还原NuGet包...
dotnet restore
if errorlevel 1 (
    echo 错误：NuGet包还原失败
    pause
    exit /b 1
)

:: 构建项目
echo 正在构建项目...
dotnet build -c Release
if errorlevel 1 (
    echo 错误：项目构建失败
    pause
    exit /b 1
)

:: 发布框架依赖版本
echo 正在发布框架依赖版本...
dotnet publish -c Release -f %FRAMEWORK% -o %OUTPUT_DIR%\framework-dependent
if errorlevel 1 (
    echo 错误：框架依赖版本发布失败
    pause
    exit /b 1
)

:: 发布自包含版本
echo 正在发布自包含版本...
dotnet publish -c Release -f %FRAMEWORK% -r %RUNTIME% --self-contained true -o %OUTPUT_DIR%\self-contained
if errorlevel 1 (
    echo 错误：自包含版本发布失败
    pause
    exit /b 1
)

:: 发布单文件版本
echo 正在发布单文件版本...
dotnet publish -c Release -f %FRAMEWORK% -r %RUNTIME% --self-contained true -p:PublishSingleFile=true -p:PublishReadyToRun=true -o %OUTPUT_DIR%\single-file
if errorlevel 1 (
    echo 错误：单文件版本发布失败
    pause
    exit /b 1
)

:: 显示构建结果
echo.
echo ========================================
echo 构建完成！
echo ========================================
echo 输出目录：%OUTPUT_DIR%
echo.
echo 可用版本：
echo 1. 框架依赖版本：%OUTPUT_DIR%\framework-dependent\%PROJECT_NAME%.exe
echo 2. 自包含版本：%OUTPUT_DIR%\self-contained\%PROJECT_NAME%.exe
echo 3. 单文件版本：%OUTPUT_DIR%\single-file\%PROJECT_NAME%.exe
echo.
echo 推荐使用单文件版本进行分发。
echo ========================================

:: 询问是否运行测试
set /p choice="是否运行单文件版本进行测试？(y/n): "
if /i "%choice%"=="y" (
    echo 正在启动单文件版本...
    start "" "%OUTPUT_DIR%\single-file\%PROJECT_NAME%.exe"
)

pause
