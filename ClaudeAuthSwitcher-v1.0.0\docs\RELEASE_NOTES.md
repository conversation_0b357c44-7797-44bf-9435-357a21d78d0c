# Claude Auth Switcher v1.0.0 发布说明

## 🎉 首次发布

Claude Auth Switcher 是一个专为开发者设计的Windows桌面应用程序，用于快速切换Claude Code IDE插件的不同认证方式。

## ✨ 主要功能

### 🔐 三种认证方式支持
- **OAuth 标准登录**: 使用Claude官方OAuth认证流程
- **AnyRouter 代理**: 通过AnyRouter代理服务访问Claude API
- **Moonshot 代理**: 通过Moonshot代理服务访问Claude API

### ⚙️ 灵活的环境变量管理
- **用户级别设置**: 仅对当前用户生效，无需管理员权限
- **系统级别设置**: 对所有用户生效，自动处理UAC权限提升
- **智能权限检查**: 自动检测所需权限并提示用户

### 📊 实时状态监控
- **环境变量状态显示**: 实时显示当前环境变量配置
- **敏感信息脱敏**: 安全显示API密钥（显示前6位和后4位）
- **配置验证**: 自动验证环境变量设置是否正确
- **连通性检查**: 可选的API端点连通性测试

### 🎨 现代化用户界面
- **直观的操作界面**: 清晰的按钮布局和状态显示
- **详细的操作反馈**: 实时显示操作结果和验证信息
- **快捷键支持**: 支持键盘快捷键操作
- **工具提示**: 完整的操作指导和快捷键提示

### ⌨️ 快捷键支持
- `Ctrl+1`: 切换到OAuth标准登录
- `Ctrl+2`: 切换到AnyRouter代理
- `Ctrl+3`: 切换到Moonshot代理
- `F5` 或 `Ctrl+R`: 刷新状态
- `Ctrl+Delete`: 清除所有环境变量
- `Esc`: 退出应用程序

### 💾 用户设置持久化
- **自动保存设置**: 记住用户的环境变量级别选择
- **配置历史**: 保存最后使用的认证配置
- **窗口状态**: 保存窗口大小和位置设置

## 🛠️ 技术特性

### 核心技术栈
- **.NET 6.0**: 现代化的.NET平台
- **WPF**: Windows Presentation Foundation UI框架
- **C#**: 主要开发语言

### 系统集成
- **Windows API集成**: 直接调用Windows环境变量API
- **UAC权限管理**: 智能处理管理员权限提升
- **系统通知**: 通知其他应用程序环境变量已更改

### 性能优化
- **快速启动**: 应用程序启动时间 < 3秒
- **低内存占用**: 运行时内存占用 < 50MB
- **异步操作**: 网络连通性检查等耗时操作异步执行

## 📦 发布版本

### 推荐版本：单文件版本
- **文件大小**: ~100MB
- **部署方式**: 单个可执行文件，无需安装
- **运行要求**: Windows 10/11 x64，无需安装.NET运行时
- **启动速度**: 首次启动稍慢（需解压），后续启动快速

### 其他版本
- **框架依赖版本**: ~10MB，需要安装.NET 6.0运行时
- **自包含版本**: ~100MB，包含.NET运行时但非单文件

## 🔧 系统要求

### 最低要求
- **操作系统**: Windows 10 1809 或更高版本
- **架构**: x64 (64位)
- **内存**: 512MB RAM
- **磁盘空间**: 200MB 可用空间

### 推荐配置
- **操作系统**: Windows 11
- **内存**: 2GB RAM 或更多
- **磁盘空间**: 500MB 可用空间

## 🚀 安装和使用

### 安装方式
1. 下载 `ClaudeAuthSwitcher.exe` 单文件版本
2. 将文件放置到任意目录
3. 双击运行，无需安装过程

### 首次使用
1. 启动应用程序
2. 选择环境变量级别（用户级别或系统级别）
3. 点击需要的认证方式按钮
4. 查看操作结果和验证信息
5. 确认配置生效

### 权限说明
- **用户级别操作**: 无需特殊权限，推荐使用
- **系统级别操作**: 需要管理员权限，应用会自动提示并处理UAC

## 🔒 安全说明

### API密钥管理
- API密钥硬编码在程序中，请确保从可信来源获取程序
- 建议定期更换API密钥
- 敏感信息在界面上脱敏显示

### 网络安全
- 不要在不安全的网络环境下使用
- 连通性检查功能可选，可以关闭
- 所有网络请求都有超时限制

### 权限安全
- 遵循最小权限原则，仅在必要时请求管理员权限
- 所有权限操作都有用户确认步骤
- 支持UAC (用户账户控制) 集成

## 🐛 已知问题

### 当前版本限制
- 仅支持Windows平台
- API密钥硬编码，无法自定义
- 不支持自动更新功能

### 故障排除
- 如果权限提升失败，请手动以管理员身份运行
- 如果环境变量设置失败，请检查系统权限
- 如果连通性检查失败，请检查网络连接和防火墙设置

## 📞 支持和反馈

### 获取帮助
- 查看应用程序内的工具提示和状态信息
- 参考 README.md 文档
- 检查操作结果区域的详细错误信息

### 报告问题
- 请提供详细的错误信息和操作步骤
- 包含系统环境信息（Windows版本、权限级别等）
- 如果可能，请提供应用程序日志

## 🔮 未来计划

### v1.1 计划功能
- 添加更多代理服务支持
- 实现配置导入/导出功能
- 添加操作日志记录
- 改进错误处理和用户提示

### v2.0 愿景
- 支持自定义认证配置
- 添加自动更新功能
- 实现配置同步功能
- 支持插件扩展

## 📄 许可证

本项目仅供学习和个人使用。请遵守相关API服务的使用条款。

---

**发布日期**: 2024年1月  
**版本**: v1.0.0  
**构建**: Release  
**平台**: Windows x64
