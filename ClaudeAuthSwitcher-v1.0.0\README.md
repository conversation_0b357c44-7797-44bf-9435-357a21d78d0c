# Claude Auth Switcher

Claude Code认证切换器 - 快速切换Claude Code IDE插件的认证方式

## 项目概述

这是一个Windows桌面应用程序，专为开发者设计，用于快速切换Claude Code IDE插件的不同认证方式，提高开发效率并简化认证配置管理。

## 功能特性

- ✅ **三种认证方式快速切换**
  - OAuth 标准登录（Claude官方认证）
  - AnyRouter 代理认证
  - Moonshot 代理认证

- ✅ **灵活的权限管理**
  - 支持用户级别和系统级别环境变量设置
  - 智能权限检查和UAC提升

- ✅ **实时状态监控**
  - 显示当前环境变量状态
  - 配置验证和错误提示

- ✅ **现代化用户界面**
  - 直观的操作界面
  - 详细的操作反馈

## 技术栈

- **开发语言**: C# (.NET 6.0)
- **UI框架**: WPF (Windows Presentation Foundation)
- **目标平台**: Windows 10/11 x64

## 项目结构

```
ClaudeAuthSwitcher/
├── Models/
│   └── AuthConfig.cs              # 认证配置模型
├── Services/
│   ├── EnvironmentManager.cs      # 环境变量管理服务
│   └── PermissionHelper.cs        # 权限检查和提升服务
├── MainWindow.xaml                # 主界面设计
├── MainWindow.xaml.cs             # 主界面逻辑
├── App.xaml                       # 应用程序配置
├── App.xaml.cs                    # 应用程序逻辑
└── Properties/
    └── Settings.settings          # 用户设置存储
```

## 构建和运行

### 前置要求

- .NET 6.0 SDK
- Windows 10/11 操作系统

### 构建项目

```bash
# 还原NuGet包
dotnet restore

# 构建项目
dotnet build

# 运行项目
dotnet run
```

### 发布单文件版本

```bash
# 发布自包含的单文件版本
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

## 使用说明

1. **选择环境变量级别**
   - 用户级别：仅对当前用户生效，无需管理员权限
   - 系统级别：对所有用户生效，需要管理员权限

2. **选择认证方式**
   - 点击对应的认证方式按钮
   - 系统会自动应用相应的环境变量配置

3. **查看状态**
   - 实时查看当前环境变量状态
   - 查看操作结果和验证信息

4. **其他操作**
   - 刷新状态：更新当前状态显示
   - 清除所有：一键清除所有Claude相关环境变量

## 安全说明

- API密钥硬编码在程序中，请确保从可信来源获取程序
- 建议定期更换API密钥
- 不要在不安全的网络环境下使用

## 版本历史

- **v1.0.0** - 初始版本
  - 实现三种认证方式切换
  - 支持用户级别和系统级别设置
  - 权限管理和状态监控

## 许可证

本项目仅供学习和个人使用。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
