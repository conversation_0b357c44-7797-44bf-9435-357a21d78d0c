using System;
using System.Diagnostics;
using System.Security.Principal;
using System.Windows;

namespace ClaudeAuthSwitcher.Services
{
    /// <summary>
    /// 权限检查和提升服务
    /// 处理UAC权限管理和管理员权限检查
    /// </summary>
    public static class PermissionHelper
    {
        /// <summary>
        /// 检查当前进程是否以管理员权限运行
        /// </summary>
        /// <returns>true表示具有管理员权限</returns>
        public static bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查管理员权限失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否需要管理员权限
        /// </summary>
        /// <param name="isSystemLevel">是否为系统级别操作</param>
        /// <returns>true表示需要管理员权限</returns>
        public static bool RequiresAdministratorPrivileges(bool isSystemLevel)
        {
            return isSystemLevel && !IsRunningAsAdministrator();
        }

        /// <summary>
        /// 以管理员权限重启应用程序
        /// </summary>
        /// <param name="arguments">启动参数</param>
        /// <returns>true表示重启成功</returns>
        public static bool RestartAsAdministrator(string arguments = "")
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = Environment.ProcessPath ?? Process.GetCurrentProcess().MainModule?.FileName,
                    Arguments = arguments,
                    UseShellExecute = true,
                    Verb = "runas", // 请求管理员权限
                    WorkingDirectory = Environment.CurrentDirectory
                };

                Process.Start(processInfo);
                
                // 关闭当前应用程序
                Application.Current.Shutdown();
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"以管理员权限重启失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 请求管理员权限（如果需要）
        /// </summary>
        /// <param name="isSystemLevel">是否为系统级别操作</param>
        /// <param name="operationName">操作名称，用于用户提示</param>
        /// <returns>true表示已获得所需权限</returns>
        public static bool RequestAdministratorPrivilegesIfNeeded(bool isSystemLevel, string operationName)
        {
            if (!RequiresAdministratorPrivileges(isSystemLevel))
            {
                return true; // 不需要管理员权限或已经具有权限
            }

            // 显示权限提升确认对话框
            var result = MessageBox.Show(
                $"执行"{operationName}"需要管理员权限。\n\n" +
                "点击"是"将以管理员身份重启应用程序。\n" +
                "点击"否"将取消此操作。",
                "需要管理员权限",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question,
                MessageBoxResult.No);

            if (result == MessageBoxResult.Yes)
            {
                return RestartAsAdministrator();
            }

            return false; // 用户拒绝权限提升
        }

        /// <summary>
        /// 获取当前用户权限状态描述
        /// </summary>
        /// <returns>权限状态描述</returns>
        public static string GetCurrentPrivilegeStatus()
        {
            if (IsRunningAsAdministrator())
            {
                return "管理员权限 - 可以修改系统级别环境变量";
            }
            else
            {
                return "标准用户权限 - 只能修改用户级别环境变量";
            }
        }

        /// <summary>
        /// 检查操作权限并给出建议
        /// </summary>
        /// <param name="isSystemLevel">是否为系统级别操作</param>
        /// <returns>权限检查结果和建议</returns>
        public static (bool CanProceed, string Message) CheckOperationPermission(bool isSystemLevel)
        {
            if (!isSystemLevel)
            {
                return (true, "用户级别操作，无需额外权限");
            }

            if (IsRunningAsAdministrator())
            {
                return (true, "已具有管理员权限，可以执行系统级别操作");
            }
            else
            {
                return (false, "系统级别操作需要管理员权限，请以管理员身份运行程序");
            }
        }
    }
}
