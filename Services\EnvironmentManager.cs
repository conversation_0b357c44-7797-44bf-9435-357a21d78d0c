using ClaudeAuthSwitcher.Models;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;

namespace ClaudeAuthSwitcher.Services
{
    /// <summary>
    /// 环境变量管理服务
    /// 负责设置、获取和清除系统环境变量
    /// </summary>
    public class EnvironmentManager
    {
        #region Windows API 声明

        /// <summary>
        /// 发送消息到所有顶级窗口，通知环境变量已更改
        /// </summary>
        [DllImport("user32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern IntPtr SendMessageTimeout(
            IntPtr hWnd,
            uint Msg,
            UIntPtr wParam,
            string lParam,
            uint fuFlags,
            uint uTimeout,
            out UIntPtr lpdwResult);

        private const int HWND_BROADCAST = 0xffff;
        private const uint WM_SETTINGCHANGE = 0x001a;
        private const uint SMTO_ABORTIFHUNG = 0x0002;

        #endregion
        /// <summary>
        /// 环境变量目标类型
        /// </summary>
        public enum VariableTarget
        {
            User,    // 用户级别
            Machine  // 系统级别
        }

        /// <summary>
        /// 设置环境变量
        /// </summary>
        /// <param name="name">变量名</param>
        /// <param name="value">变量值，null表示删除变量</param>
        /// <param name="target">目标级别</param>
        /// <returns>操作是否成功</returns>
        public static bool SetEnvironmentVariable(string name, string? value, VariableTarget target)
        {
            try
            {
                var envTarget = target == VariableTarget.User 
                    ? EnvironmentVariableTarget.User 
                    : EnvironmentVariableTarget.Machine;

                Environment.SetEnvironmentVariable(name, value, envTarget);
                
                // 通知系统环境变量已更改
                NotifyEnvironmentChange();
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"设置环境变量失败: {name} = {value}, 错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取环境变量值
        /// </summary>
        /// <param name="name">变量名</param>
        /// <param name="target">目标级别</param>
        /// <returns>变量值，不存在返回null</returns>
        public static string? GetEnvironmentVariable(string name, VariableTarget target)
        {
            try
            {
                var envTarget = target == VariableTarget.User 
                    ? EnvironmentVariableTarget.User 
                    : EnvironmentVariableTarget.Machine;

                return Environment.GetEnvironmentVariable(name, envTarget);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取环境变量失败: {name}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 批量设置环境变量
        /// </summary>
        /// <param name="variables">变量字典</param>
        /// <param name="target">目标级别</param>
        /// <returns>操作结果</returns>
        public static Dictionary<string, bool> SetEnvironmentVariables(
            Dictionary<string, string?> variables,
            VariableTarget target)
        {
            var results = new Dictionary<string, bool>();

            foreach (var kvp in variables)
            {
                results[kvp.Key] = SetEnvironmentVariable(kvp.Key, kvp.Value, target);
            }

            return results;
        }

        /// <summary>
        /// 批量设置环境变量（增强版，返回详细结果）
        /// </summary>
        /// <param name="variables">变量字典</param>
        /// <param name="target">目标级别</param>
        /// <returns>批量操作结果</returns>
        public static BatchOperationResult SetEnvironmentVariablesEnhanced(
            Dictionary<string, string?> variables,
            VariableTarget target)
        {
            var batchResult = new BatchOperationResult();

            foreach (var kvp in variables)
            {
                try
                {
                    var success = SetEnvironmentVariable(kvp.Key, kvp.Value, target);
                    var action = kvp.Value == null ? "清除" : "设置";

                    if (success)
                    {
                        batchResult.AddResult(OperationResult.Success(
                            $"{action}环境变量 {kvp.Key} 成功",
                            $"目标级别: {(target == VariableTarget.User ? "用户级别" : "系统级别")}",
                            $"操作: {action}",
                            $"值: {(kvp.Value ?? "(清除)")}"
                        ));
                    }
                    else
                    {
                        batchResult.AddResult(OperationResult.Error(
                            $"{action}环境变量 {kvp.Key} 失败",
                            null,
                            $"目标级别: {(target == VariableTarget.User ? "用户级别" : "系统级别")}",
                            $"操作: {action}",
                            $"值: {(kvp.Value ?? "(清除)")}"
                        ));
                    }
                }
                catch (Exception ex)
                {
                    var action = kvp.Value == null ? "清除" : "设置";
                    batchResult.AddResult(OperationResult.Error(
                        $"{action}环境变量 {kvp.Key} 时发生异常",
                        ex,
                        $"目标级别: {(target == VariableTarget.User ? "用户级别" : "系统级别")}",
                        $"操作: {action}",
                        $"值: {(kvp.Value ?? "(清除)")}"
                    ));
                }
            }

            return batchResult;
        }

        /// <summary>
        /// 获取当前所有Claude相关环境变量状态
        /// </summary>
        /// <param name="target">目标级别</param>
        /// <returns>环境变量状态字典</returns>
        public static Dictionary<string, string?> GetClaudeEnvironmentStatus(VariableTarget target)
        {
            var claudeVars = new[] 
            { 
                "ANTHROPIC_AUTH_TOKEN", 
                "ANTHROPIC_BASE_URL", 
                "ANTHROPIC_API_KEY" 
            };
            
            var status = new Dictionary<string, string?>();
            
            foreach (var varName in claudeVars)
            {
                status[varName] = GetEnvironmentVariable(varName, target);
            }
            
            return status;
        }

        /// <summary>
        /// 清除所有Claude相关环境变量
        /// </summary>
        /// <param name="target">目标级别</param>
        /// <returns>操作结果</returns>
        public static Dictionary<string, bool> ClearClaudeEnvironmentVariables(VariableTarget target)
        {
            var claudeVars = new Dictionary<string, string?>
            {
                { "ANTHROPIC_AUTH_TOKEN", null },
                { "ANTHROPIC_BASE_URL", null },
                { "ANTHROPIC_API_KEY", null }
            };
            
            return SetEnvironmentVariables(claudeVars, target);
        }

        /// <summary>
        /// 通知系统环境变量已更改
        /// </summary>
        private static void NotifyEnvironmentChange()
        {
            try
            {
                // 通知所有应用程序环境变量已更改
                SendMessageTimeout(
                    (IntPtr)HWND_BROADCAST,
                    WM_SETTINGCHANGE,
                    UIntPtr.Zero,
                    "Environment",
                    SMTO_ABORTIFHUNG,
                    5000,
                    out _);

                Debug.WriteLine("环境变量更改通知已发送");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"发送环境变量更改通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证环境变量配置是否正确
        /// </summary>
        /// <param name="expectedConfig">期望的配置</param>
        /// <param name="target">目标级别</param>
        /// <returns>验证结果</returns>
        public static Dictionary<string, bool> ValidateConfiguration(
            Dictionary<string, string?> expectedConfig, 
            VariableTarget target)
        {
            var results = new Dictionary<string, bool>();
            
            foreach (var kvp in expectedConfig)
            {
                var actualValue = GetEnvironmentVariable(kvp.Key, target);
                results[kvp.Key] = string.Equals(actualValue, kvp.Value, StringComparison.Ordinal);
            }
            
            return results;
        }
    }
}
