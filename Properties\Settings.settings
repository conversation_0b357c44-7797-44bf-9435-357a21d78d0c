<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="ClaudeAuthSwitcher.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="LastSelectedTarget" Type="System.String" Scope="User">
      <Value Profile="(Default)">User</Value>
    </Setting>
    <Setting Name="LastUsedAuthConfig" Type="System.String" Scope="User">
      <Value Profile="(Default)">OAuth</Value>
    </Setting>
    <Setting Name="WindowWidth" Type="System.Double" Scope="User">
      <Value Profile="(Default)">800</Value>
    </Setting>
    <Setting Name="WindowHeight" Type="System.Double" Scope="User">
      <Value Profile="(Default)">600</Value>
    </Setting>
  </Settings>
</SettingsFile>
