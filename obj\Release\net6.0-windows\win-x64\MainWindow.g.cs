﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EC97E0BF59527DB98051E78BF4A0364C80BDC57F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ClaudeAuthSwitcher {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 175 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton UserLevelRadio;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton SystemLevelRadio;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PermissionStatusText;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OAuthButton;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AnyRouterButton;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MoonshotButton;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResultText;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearAllButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ClaudeAuthSwitcher;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\..\MainWindow.xaml"
            ((ClaudeAuthSwitcher.MainWindow)(target)).KeyDown += new System.Windows.Input.KeyEventHandler(this.MainWindow_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.UserLevelRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 179 "..\..\..\..\MainWindow.xaml"
            this.UserLevelRadio.Checked += new System.Windows.RoutedEventHandler(this.TargetLevel_Changed);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SystemLevelRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 182 "..\..\..\..\MainWindow.xaml"
            this.SystemLevelRadio.Checked += new System.Windows.RoutedEventHandler(this.TargetLevel_Changed);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PermissionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.OAuthButton = ((System.Windows.Controls.Button)(target));
            
            #line 204 "..\..\..\..\MainWindow.xaml"
            this.OAuthButton.Click += new System.Windows.RoutedEventHandler(this.OAuthButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AnyRouterButton = ((System.Windows.Controls.Button)(target));
            
            #line 215 "..\..\..\..\MainWindow.xaml"
            this.AnyRouterButton.Click += new System.Windows.RoutedEventHandler(this.AnyRouterButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.MoonshotButton = ((System.Windows.Controls.Button)(target));
            
            #line 226 "..\..\..\..\MainWindow.xaml"
            this.MoonshotButton.Click += new System.Windows.RoutedEventHandler(this.MoonshotButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ResultText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 273 "..\..\..\..\MainWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ClearAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 278 "..\..\..\..\MainWindow.xaml"
            this.ClearAllButton.Click += new System.Windows.RoutedEventHandler(this.ClearAllButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

